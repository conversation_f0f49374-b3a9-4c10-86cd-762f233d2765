package com.tui.destilink.framework.locking.redis.lock.impl;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.Mockito.*;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for RedisStateLock.
 * This is a pure unit test that doesn't load any Spring context.
 */
class RedisStateLockTest {

    private static final String LOCK_KEY = "test:__locks__:state:{test-key}";
    private static final String EXPECTED_STATE = "INITIAL";
    private static final String NEW_STATE = "UPDATED";
    private static final String OWNER_ID = "test-owner";
    private static final Duration STATE_EXPIRATION = Duration.ofSeconds(30);
    private static final String STATE_KEY_SUFFIX = ":state";
    private static final String RESPONSE_CACHE_SUFFIX = ":response_cache";

    @Mock
    private RedisLockOperations redisLockOperations;

    @Mock
    private LockOwnerSupplier lockOwnerSupplier;

    @Mock
    private ExecutorService virtualThreadExecutor;

    @Mock
    private LockWatchdog lockWatchdog;

    private RedisLockProperties properties;
    private RedisStateLock stateLock;

    @BeforeEach
    void setUp() {
        // Initialize mocks
        MockitoAnnotations.openMocks(this);

        // Create properly configured RedisLockProperties using default constructors
        RedisLockProperties.Defaults defaults = new RedisLockProperties.Defaults();
        // Set custom values if needed
        defaults.setLeaseTime(Duration.ofSeconds(30));
        defaults.setRetryInterval(Duration.ofMillis(100));
        defaults.setMaxRetries(3);
        defaults.setAcquireTimeout(Duration.ofSeconds(30));

        RedisLockProperties.WatchdogProperties watchdog = new RedisLockProperties.WatchdogProperties();
        // Set custom values if needed
        watchdog.setInterval(Duration.ofSeconds(10));
        watchdog.setFactor(0.3);
        watchdog.setCorePoolSize(2);
        watchdog.setThreadNamePrefix("test-watchdog-");
        watchdog.setShutdownAwaitTermination(Duration.ofSeconds(30));

        RedisLockProperties.RetryConfig retry = new RedisLockProperties.RetryConfig();
        // Set custom values if needed
        retry.setMaxAttempts(3);
        retry.setInitialDelay(Duration.ofMillis(100));
        retry.setBackoffMultiplier(2.0);
        retry.setMaxDelay(Duration.ofSeconds(5));
        retry.setJitterEnabled(true);
        retry.setCircuitBreakerFailureThreshold(5);
        retry.setCircuitBreakerRecoveryTimeout(Duration.ofSeconds(30));

        properties = new RedisLockProperties(
                true,                      // enabled
                Duration.ofMinutes(5),     // stateKeyExpiration
                defaults,
                watchdog,
                Duration.ofSeconds(60),    // responseCacheTtl
                retry
        );

        when(lockOwnerSupplier.get()).thenReturn(OWNER_ID);

        stateLock = new RedisStateLock(
                redisLockOperations,
                lockOwnerSupplier,
                properties,
                LOCK_KEY,
                30000L, // lockTtlMillis
                100L,   // retryIntervalMillis
                3,      // maxRetries
                virtualThreadExecutor,
                lockWatchdog);
    }

    @Test
    void shouldUseProperUnlockChannelConstruction() {
        // Given
        when(redisLockOperations.unlockStateLock(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(NEW_STATE),
                eq(STATE_KEY_SUFFIX),
                anyString(),
                anyString())).thenReturn(CompletableFuture.completedFuture(null));

        // When
        stateLock.unlockAsync().join();

        // Then
        ArgumentCaptor<String> channelCaptor = ArgumentCaptor.forClass(String.class);
        verify(redisLockOperations).unlockStateLock(
                eq(LOCK_KEY),
                channelCaptor.capture(),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(NEW_STATE),
                eq(STATE_KEY_SUFFIX),
                anyString(),
                anyString());

        String capturedChannel = channelCaptor.getValue();
        assertThat(capturedChannel).isEqualTo("test:__unlock_channels__:state:{test-key}");
    }

    @Test
    void shouldUseUuidForRequestIdInsteadOfNanoTime() {
        // Given
        when(redisLockOperations.tryStateLock(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                anyBoolean(),
                anyString(),
                anyString())).thenReturn(CompletableFuture.completedFuture(null));

        // When
        stateLock.tryLockAsync().join();

        // Then
        ArgumentCaptor<String> requestUuidCaptor = ArgumentCaptor.forClass(String.class);
        verify(redisLockOperations).tryStateLock(
                eq(LOCK_KEY),
                anyString(),
                requestUuidCaptor.capture(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                anyBoolean(),
                anyString(),
                anyString());

        String capturedUuid = requestUuidCaptor.getValue();
        assertThat(capturedUuid).matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}");
    }

    @Test
    void shouldPropagateThreadInterruption() {
        // Given
        Thread.currentThread().interrupt();

        // When/Then
        assertThatThrownBy(() -> stateLock.lockInterruptiblyAsync())
                .isInstanceOf(LockAcquisitionException.class)
                .hasMessageContaining("Thread already interrupted");

        // Clear the interrupted flag
        assertThat(Thread.interrupted()).isTrue();
    }

    @Test
    void shouldUseAtomicStateUpdates() {
        // Given
        when(redisLockOperations.updateStateIfEquals(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                eq(NEW_STATE),
                eq(STATE_KEY_SUFFIX),
                anyString(),
                anyString())).thenReturn(CompletableFuture.completedFuture(1));

        // When
        boolean result = stateLock.updateStateIfEqualsAsync(EXPECTED_STATE, NEW_STATE).join();

        // Then
        assertThat(result).isTrue();
        verify(redisLockOperations).updateStateIfEquals(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                eq(NEW_STATE),
                eq(STATE_KEY_SUFFIX),
                anyString(),
                anyString());
    }

    @Test
    void shouldHandleStateMismatchInAtomicUpdate() {
        // Given
        when(redisLockOperations.updateStateIfEquals(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                eq(NEW_STATE),
                eq(STATE_KEY_SUFFIX),
                anyString(),
                anyString())).thenReturn(CompletableFuture.completedFuture(-1));

        // When
        boolean result = stateLock.updateStateIfEqualsAsync(EXPECTED_STATE, NEW_STATE).join();

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void shouldSynchronizeStateTtlWithLockTtl() {
        // Given
        when(redisLockOperations.getString(LOCK_KEY + STATE_KEY_SUFFIX))
                .thenReturn(CompletableFuture.completedFuture(EXPECTED_STATE));

        when(redisLockOperations.updateState(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                eq(STATE_KEY_SUFFIX),
                anyString(),
                anyString())).thenReturn(CompletableFuture.completedFuture(1));

        // When
        stateLock.updateLockState(OWNER_ID).join();

        // Then
        ArgumentCaptor<String> ttlCaptor = ArgumentCaptor.forClass(String.class);
        verify(redisLockOperations).updateState(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                eq(STATE_KEY_SUFFIX),
                ttlCaptor.capture(),
                anyString());

        String capturedTtl = ttlCaptor.getValue();
        assertThat(capturedTtl).isEqualTo(String.valueOf(STATE_EXPIRATION.toMillis()));
    }

    @Test
    void shouldHandleConcurrentStateUpdates() throws ExecutionException, InterruptedException {
        // Given
        int numThreads = 5;
        AtomicInteger successCount = new AtomicInteger(0);

        when(redisLockOperations.updateStateIfEquals(
                eq(LOCK_KEY),
                anyString(),
                anyString(),
                eq(OWNER_ID),
                eq(EXPECTED_STATE),
                eq(NEW_STATE),
                eq(STATE_KEY_SUFFIX),
                anyString(),
                anyString())).thenAnswer(invocation -> {
                    // Simulate that only one thread succeeds
                    if (successCount.getAndIncrement() == 0) {
                        return CompletableFuture.completedFuture(1);
                    } else {
                        return CompletableFuture.completedFuture(0);
                    }
                });

        // When
        @SuppressWarnings("unchecked")
        CompletableFuture<Boolean>[] futures = new CompletableFuture[numThreads];
        for (int i = 0; i < numThreads; i++) {
            futures[i] = stateLock.updateStateIfEqualsAsync(EXPECTED_STATE, NEW_STATE);
        }

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures);
        allFutures.get(); // Wait for all futures to complete

        // Then
        int successfulUpdates = 0;
        for (CompletableFuture<Boolean> future : futures) {
            if (future.get()) {
                successfulUpdates++;
            }
        }

        assertThat(successfulUpdates).isEqualTo(1);
    }

    @Test
    void shouldHandleStateExpirationScenarios() {
        // Given
        when(redisLockOperations.getString(LOCK_KEY + STATE_KEY_SUFFIX))
                .thenReturn(CompletableFuture.completedFuture(null)); // State expired or doesn't exist

        // When
        stateLock.updateLockState(OWNER_ID).join();

        // Then
        verify(redisLockOperations).getString(LOCK_KEY + STATE_KEY_SUFFIX);
        verify(redisLockOperations, never()).updateState(
                anyString(), anyString(), anyString(), anyString(),
                anyString(), anyString(), anyString(), anyString());
    }
}