debug: true
spring:
  application:
    name: test-redis-lock

destilink:
  fw:
    redis:
      core:
        keyspace-prefixes:
          application: test-redis-lock
          distributed: __distributed__
    locking:
      redis:
        enabled: true
        state-key-expiration: PT5M
        response-cache-ttl: PT1M
        defaults:
          lease-time: PT60S
          retry-interval: PT0.1S
          max-retries: 3
          acquire-timeout: PT10S
        watchdog:
          interval: PT5S
          factor: 0.3
          core-pool-size: 2
          thread-name-prefix: "test-lock-watchdog-"
          shutdown-await-termination: PT30S
        retry:
          max-attempts: 3
          initial-delay: PT0.1S
          backoff-multiplier: 2.0
          max-delay: PT5S
          jitter-enabled: true
          circuit-breaker-failure-threshold: 5
          circuit-breaker-recovery-timeout: PT30S
