package com.tui.destilink.framework.locking.redis.lock;

import com.tui.destilink.framework.locking.redis.lock.config.RedisLockProperties;
import com.tui.destilink.framework.locking.redis.lock.exception.LockAcquisitionException;
import com.tui.destilink.framework.locking.redis.lock.exception.LockReleaseException;
import com.tui.destilink.framework.locking.redis.lock.service.LockOwnerSupplier;
import com.tui.destilink.framework.locking.redis.lock.service.LockSemaphoreHolder;
import com.tui.destilink.framework.locking.redis.lock.service.LockWatchdog;
import com.tui.destilink.framework.locking.redis.lock.service.RedisLockOperations;
import com.tui.destilink.framework.locking.redis.lock.util.VirtualThreadContextUtils;
import com.tui.destilink.framework.locking.redis.lock.util.RedisLockContextDecorator;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.locks.Condition;

/**
 * Abstract base class for Redis-based lock implementations.
 * <p>
 * * This class provides common functionality for Redis-based locks,
 * implementing
 * both
 * the {@link AsyncLock} interface for asynchronous operations and the standard
 * {@link java.util.concurrent.locks.Lock} interface for synchronous operations.
 * </p>
 * <p>
 * The synchronous methods act as blocking wrappers around the asynchronous
 * operations,
 * using {@link CompletableFuture#join()} or {@link CompletableFuture#get()} to
 * wait for completion.
 * </p>
 * <p>
 * Concrete subclasses must implement the abstract methods to provide specific
 * locking
 * behavior.
 * </p>
 */
@Slf4j
public abstract class AbstractRedisLock implements AsyncLock {

    protected static final String RESPONSE_CACHE_SUFFIX = ":response_cache";

    protected final RedisLockOperations redisLockOperations;
    protected final LockOwnerSupplier lockOwnerSupplier;
    protected final RedisLockProperties properties;
    protected final ExecutorService virtualThreadExecutor;
    protected final LockWatchdog watchdog; // Can be null if watchdog is not available

    // Class hierarchy specifies these field names
    protected final String lockName; // Alias for lockKey for compatibility
    protected final String ownerId; // Current owner identifier

    protected final String lockKey;
    protected final long lockTtlMillis;
    protected final long retryIntervalMillis;
    protected final int maxRetries;

    // Common fields for all lock implementations
    protected final LockSemaphoreHolder lockSemaphoreHolder;

    /**
     * Creates a new Redis-based lock with the specified key and default
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     * @param virtualThreadExecutor Virtual Thread executor for async operations
     */
    protected AbstractRedisLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        this(redisLockOperations, lockOwnerSupplier, properties, lockKey,
                properties.getDefaults().getLeaseTime().toMillis(),
                properties.getDefaults().getRetryInterval().toMillis(),
                properties.getDefaults().getMaxRetries() > 0 ? properties.getDefaults().getMaxRetries() : 10,
                virtualThreadExecutor, watchdog);
    }

    /**
     * Creates a new Redis-based lock with the specified key and custom
     * configuration.
     *
     * @param redisLockOperations Redis lock operations for executing lock commands
     * @param lockOwnerSupplier   Supplier for lock owner identifiers
     * @param properties          Redis lock configuration properties
     * @param lockKey             The unique key identifying this lock in Redis
     * @param lockTtlMillis       Time-to-live for the lock in milliseconds
     * @param retryIntervalMillis Interval between retry attempts in milliseconds
     * @param maxRetries          Maximum number of retry attempts
     * @param virtualThreadExecutor Virtual Thread executor for async operations
     */
    protected AbstractRedisLock(
            RedisLockOperations redisLockOperations,
            LockOwnerSupplier lockOwnerSupplier,
            RedisLockProperties properties,
            String lockKey,
            long lockTtlMillis,
            long retryIntervalMillis,
            int maxRetries,
            ExecutorService virtualThreadExecutor,
            LockWatchdog watchdog) {
        this.redisLockOperations = redisLockOperations;
        this.lockOwnerSupplier = lockOwnerSupplier;
        this.properties = properties;
        this.lockKey = lockKey;
        this.lockTtlMillis = lockTtlMillis;
        this.retryIntervalMillis = retryIntervalMillis;
        this.maxRetries = maxRetries;
        this.virtualThreadExecutor = Objects.requireNonNull(virtualThreadExecutor, "virtualThreadExecutor cannot be null");
        this.watchdog = watchdog; // Can be null
        this.lockName = lockKey; // Initialize lockName for compatibility
        this.ownerId = lockOwnerSupplier.get(); // Initialize ownerId
        this.lockSemaphoreHolder = new LockSemaphoreHolder(); // Initialize the semaphore holder
    }

    // Abstract methods for concrete lock logic (to be implemented by subclasses)
    protected abstract CompletableFuture<Boolean> doTryLock(String ownerId, Duration effectiveTimeout);

    protected abstract CompletableFuture<Void> doLock(String ownerId, Duration effectiveTimeout);

    protected abstract CompletableFuture<Void> doUnlock(String ownerId);

    protected abstract String getLockType();

    protected abstract CompletableFuture<Void> updateLockState(String ownerId);

    // --- AsyncLock methods ---

    @Override
    public CompletableFuture<Void> lockAsync() {
        return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("async_acquire")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                // Effective timeout for lock acquisition attempts, can be long or based on
                // properties
                Duration effectiveTimeout = properties.getDefaults().getAcquireTimeout() != null
                        ? properties.getDefaults().getAcquireTimeout()
                        : Duration.ofMillis(Long.MAX_VALUE);
                return doLock(ownerId, effectiveTimeout);
            }
        }).thenCompose(future -> future);
    }

    @Override
    public CompletableFuture<Boolean> tryLockAsync() {
        return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("async_try_acquire")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                return doTryLock(ownerId, Duration.ZERO); // Immediate attempt
            }
        }).thenCompose(future -> future);
    }

    @Override
    public CompletableFuture<Boolean> tryLockAsync(long timeout, TimeUnit unit) {
        return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("async_try_acquire_timed")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                return doTryLock(ownerId, Duration.ofMillis(unit.toMillis(timeout)));
            }
        }).thenCompose(future -> future);
    }

    @Override
    public CompletableFuture<Void> unlockAsync() {
        return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("async_release")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                return doUnlock(ownerId);
            }
        }).thenCompose(future -> future);
    }

    /**
     * {@inheritDoc}
     * <p>
     * Default implementation that delegates to lockAsync() and handles
     * interruption.
     * Subclasses can override this if they need specific interruptible behavior.
     * </p>
     */
    @Override
    public CompletableFuture<Void> lockInterruptiblyAsync() {
        return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("async_acquire_interruptible")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                Duration effectiveTimeout = properties.getDefaults().getAcquireTimeout() != null
                        ? properties.getDefaults().getAcquireTimeout()
                        : Duration.ofMillis(Long.MAX_VALUE);

                return doLock(ownerId, effectiveTimeout)
                        .thenAccept(result -> {
                            if (result == null) {
                                throw new LockAcquisitionException(getLockKey(), getLockType(), ownerId,
                                        null, "Failed to acquire interruptible lock");
                            }
                        })
                        .exceptionally(ex -> {
                            if (ex instanceof CompletionException && ex.getCause() instanceof InterruptedException) {
                                Thread.currentThread().interrupt();
                                throw new LockAcquisitionException(getLockKey(), getLockType(), ownerId,
                                        null, "Lock acquisition interrupted", ex.getCause());
                            }
                            if (ex instanceof LockAcquisitionException) {
                                throw (LockAcquisitionException) ex;
                            }
                            throw new LockAcquisitionException(getLockKey(), getLockType(), ownerId,
                                    null, "Failed to acquire interruptible lock", ex);
                        });
            }
        }).thenCompose(future -> future);
    }

    /**
     * {@inheritDoc}
     * <p>
     * Default implementation that checks if the lock exists in Redis.
     * Subclasses can override this if they need specific checking behavior.
     * </p>
     */
    @Override
    public CompletableFuture<Boolean> isLockedAsync() {
        return VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("async_check_status")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                return redisLockOperations.checkLock(getLockKey(), ownerId)
                        .exceptionally(ex -> {
                            log.error("Error checking lock status: lockKey={}, ownerId={}", getLockKey(), ownerId, ex);
                            return false;
                        });
            }
        }).thenCompose(future -> future);
    }

    // --- java.util.concurrent.locks.Lock methods ---

    @Override
    public void lock() {
        CompletableFuture<Void> future = VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("acquire")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                Duration effectiveTimeout = properties.getDefaults().getAcquireTimeout() != null
                        ? properties.getDefaults().getAcquireTimeout()
                        : Duration.ofMillis(Long.MAX_VALUE);

                doLock(ownerId, effectiveTimeout).join();
                return null;
            } catch (Exception e) {
                log.error("Error acquiring lock: lockKey={}, type={}", lockKey, getLockType(), e);
                throw new LockAcquisitionException(lockKey, getLockType(), ownerId,
                        "Failed to acquire lock", e);
            }
        });

        // Block on the future to maintain synchronous behavior
        future.join();
    }

    @Override
    public void lockInterruptibly() throws InterruptedException {
        CompletableFuture<Void> future = VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("acquire_interruptibly")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                Duration effectiveTimeout = properties.getDefaults().getAcquireTimeout() != null
                        ? properties.getDefaults().getAcquireTimeout()
                        : Duration.ofMillis(Long.MAX_VALUE);
                
                return doLock(ownerId, effectiveTimeout)
                        .thenAccept(result -> {
                            if (result == null) {
                                throw new LockAcquisitionException(lockKey, getLockType(), ownerId,
                                        null, "Failed to acquire interruptible lock");
                            }
                        })
                        .exceptionally(ex -> {
                            if (ex instanceof CompletionException && ex.getCause() instanceof InterruptedException) {
                                Thread.currentThread().interrupt();
                                throw new LockAcquisitionException(lockKey, getLockType(), ownerId,
                                        null, "Lock acquisition interrupted", ex.getCause());
                            }
                            if (ex instanceof LockAcquisitionException) {
                                throw (LockAcquisitionException) ex;
                            }
                            throw new LockAcquisitionException(lockKey, getLockType(), ownerId,
                                    null, "Failed to acquire interruptible lock", ex);
                        }).join();
            } catch (Exception e) {
                if (e instanceof InterruptedException) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException(e);
                }
                log.error("Error acquiring lock interruptibly: lockKey={}, type={}", lockKey, getLockType(), e);
                throw new LockAcquisitionException(lockKey, getLockType(), ownerId,
                        "Failed to acquire lock interruptibly", e);
            }
        });
        
        try {
            future.get(); // Use get() to properly handle InterruptedException
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Lock acquisition interrupted: lockKey={}, type={}", lockKey, getLockType(), e);
            throw e;
        } catch (ExecutionException e) {
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.warn("Lock acquisition interrupted via ExecutionException: lockKey={}, type={}", lockKey,
                        getLockType(), e.getCause());
                throw (InterruptedException) e.getCause();
            }
            log.error("Execution error acquiring lock interruptibly: lockKey={}, type={}", lockKey, getLockType(),
                    e.getCause());
            throw new LockAcquisitionException(lockKey, getLockType(), ownerId,
                    "Failed to acquire lock interruptibly", e.getCause());
        }
    }

    @Override
    public boolean tryLock() {
        CompletableFuture<Boolean> future = VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("try_acquire")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                return doTryLock(ownerId, Duration.ZERO).join(); // Immediate attempt
            } catch (Exception e) {
                log.error("Error trying to acquire lock: lockKey={}, type={}", lockKey, getLockType(), e);
                // According to Lock#tryLock contract, it should not throw other exceptions.
                // However, if join() throws, it indicates a problem.
                // Depending on strictness, either return false or rethrow as a custom runtime
                // exception.
                return false;
            }
        });
        
        try {
            return future.join();
        } catch (Exception e) {
            log.error("Error trying to acquire lock: lockKey={}, type={}", lockKey, getLockType(), e);
            return false;
        }
    }

    @Override
    public boolean tryLock(long time, TimeUnit unit) throws InterruptedException {
        CompletableFuture<Boolean> future = VirtualThreadContextUtils.supplyAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("try_acquire_timed")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                Duration timeout = Duration.of(time, unit.toChronoUnit());
                return doTryLock(ownerId, timeout).join();
            }
        });
        
        try {
            // Using get() with timeout to honor InterruptedException
            return future.get(time, unit);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Lock acquisition attempt timed out or interrupted: lockKey={}, type={}", lockKey, getLockType(),
                    e);
            throw e;
        } catch (ExecutionException e) {
            if (e.getCause() instanceof InterruptedException) {
                Thread.currentThread().interrupt();
                log.warn("Lock acquisition attempt interrupted via ExecutionException: lockKey={}, type={}", lockKey,
                        getLockType(), e.getCause());
                throw (InterruptedException) e.getCause();
            }
            log.error("Execution error trying to acquire lock with timeout: lockKey={}, type={}", lockKey,
                    getLockType(),
                    e.getCause());
            // As per Lock#tryLock(long, TimeUnit) contract, other exceptions are not
            // expected.
            // Consider if false should be returned or if a runtime exception is more
            // appropriate.
            return false; // Or rethrow a custom runtime exception
        } catch (TimeoutException e) {
            log.debug("Timeout trying to acquire lock: lockKey={}, type={}", lockKey, getLockType());
            return false;
        }
    }

    @Override
    public void unlock() {
        CompletableFuture<Void> future = VirtualThreadContextUtils.runAsync(virtualThreadExecutor, () -> {
            try (var context = RedisLockContextDecorator.create()
                    .lockKey(lockKey)
                    .lockOperation("release")
                    .lockOwner(ownerId)
                    .lockTtl(lockTtlMillis)
                    .createClosableScope()) {

                doUnlock(ownerId).join();
            }
        });

        try {
            future.join();
        } catch (Exception e) {
            log.error("Error releasing lock: lockKey={}, type={}", lockKey, getLockType(), e);
            throw new LockReleaseException(lockKey, getLockType(), ownerId, "Failed to release lock",
                    e);
        }
    }

    @Override
    public Condition newCondition() {
        // This is a distributed lock, conditions are not typically supported in the
        // same
        // way as local locks.
        // Throwing UnsupportedOperationException is a common approach.
        throw new UnsupportedOperationException(
                "Conditions are not supported by this distributed lock implementation.");
    }

    // Getter methods for subclasses if needed, though direct field access is also
    // possible for protected fields.
    public String getLockKey() {
        return lockKey;
    }

    public long getLockTtlMillis() {
        return lockTtlMillis;
    }

    public long getRetryIntervalMillis() {
        return retryIntervalMillis;
    }

    public int getMaxRetries() {
        return maxRetries;
    }

    public RedisLockOperations getRedisLockOperations() {
        return redisLockOperations;
    }

    public LockOwnerSupplier getLockOwnerSupplier() {
        return lockOwnerSupplier;
    }

    public RedisLockProperties getProperties() {
        return properties;
    }

    /**
     * Determines if this lock is a read lock.
     *
     * @return true if this is a read lock, false otherwise
     */
    public boolean isReadLock() {
        return false;
    }

    /**
     * Determines if this lock is a write lock.
     *
     * @return true if this is a write lock, false otherwise
     */
    public boolean isWriteLock() {
        return false;
    }

    /**
     * Gets the response cache key for this lock.
     * This is used for caching responses to idempotent operations.
     *
     * @return the response cache key
     */
    public final String getResponseCacheKey() {
        return getLockKey() + RESPONSE_CACHE_SUFFIX;
    }

    /**
     * Gets the TTL for response cache entries in seconds.
     *
     * @return the response cache TTL as Duration
     */
    protected Duration getResponseCacheTtlSeconds() {
        return properties.getResponseCacheTtl();
    }

    // ========================================
    // WATCHDOG INTEGRATION METHODS
    // ========================================

    /**
     * Checks if this lock is eligible for watchdog monitoring based on the safety buffer.
     * A lock is eligible if: (expiresAtMillis - currentTimeMillis) <= safetyBuffer
     * where safetyBuffer = originalLeaseTimeMillis * factor
     *
     * @param currentTimeMillis Current time in milliseconds
     * @param expiresAtMillis Lock expiration time in milliseconds
     * @param originalLeaseTimeMillis Original lease time in milliseconds
     * @param factor Safety buffer factor from configuration
     * @return true if the lock is eligible for watchdog monitoring
     */
    public boolean isWatchdogEligible(long currentTimeMillis, long expiresAtMillis,
                                     long originalLeaseTimeMillis, double factor) {
        long remainingTimeMillis = expiresAtMillis - currentTimeMillis;
        long safetyBufferMillis = (long) (originalLeaseTimeMillis * factor);

        boolean eligible = remainingTimeMillis <= safetyBufferMillis;

        log.debug("Watchdog eligibility check for {}: remaining={}ms, safetyBuffer={}ms, eligible={}",
                lockKey, remainingTimeMillis, safetyBufferMillis, eligible);

        return eligible;
    }

    /**
     * Registers this lock with the watchdog for monitoring.
     * This should be called after successful lock acquisition.
     *
     * @param originalLeaseTimeMillis Original lease time in milliseconds
     */
    protected void registerWithWatchdog(long originalLeaseTimeMillis) {
        if (watchdog != null) {
            Duration leaseDuration = Duration.ofMillis(originalLeaseTimeMillis);
            log.debug("Registering lock {} with watchdog: originalLease={}",
                    lockKey, originalLeaseTimeMillis);
            watchdog.registerLock(lockKey, getLockOwnerId(), leaseDuration, lockOwnerSupplier);
        }
    }

    /**
     * Unregisters this lock from the watchdog.
     * This should be called before unlocking.
     */
    protected void unregisterFromWatchdog() {
        if (watchdog != null) {
            log.debug("Unregistering lock {} from watchdog", lockKey);
            watchdog.unregisterLock(lockKey, getLockOwnerId());
        }
    }



    /**
     * Gets the lock owner ID for watchdog operations.
     * @return The lock owner ID
     */
    public String getLockOwnerId() {
        return ownerId;
    }
}